lsp: 2025-06-20 22:20:26 UTC pid: 21976 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['--log-file=c:\\Alternative\\robot_lsp.log', '--verbose']

lsp: 2025-06-20 22:20:26 UTC pid: 21976 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

lsp: 2025-06-20 22:20:26 UTC pid: 21976 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

lsp: 2025-06-20 22:20:26 UTC pid: 21976 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkLanguageServer IO language server. pid: 21976

lsp: 2025-06-20 22:20:26 UTC pid: 21976 - MainThread - INFO - robotframework_ls.robotframework_ls_impl
Using watch implementation: watchdog (customize with ROBOTFRAMEWORK_LS_WATCH_IMPL environment variable)

lsp: 2025-06-20 22:20:26 UTC pid: 21976 - MainThread - INFO - robocorp_ls_core.remote_fs_observer_impl
Initializing Remote FS Observer with the following args: ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-u', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\vendored\\robocorp_ls_core\\remote_fs_observer__main__.py', '--log-file=c:\\Alternative\\robot_lsp.log', '-v']

lsp: 2025-06-20 22:21:39 UTC pid: 21976 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 8, method: textDocument/codeLens

lsp: 2025-06-20 22:21:39 UTC pid: 21976 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 4, method: textDocument/codeAction

lsp: 2025-06-20 22:21:39 UTC pid: 21976 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 11, method: textDocument/codeAction

lsp: 2025-06-20 22:21:39 UTC pid: 21976 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 12, method: textDocument/codeAction

lsp: 2025-06-20 22:21:39 UTC pid: 21976 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 13, method: textDocument/codeAction

lsp: 2025-06-20 22:21:39 UTC pid: 21976 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 10, method: textDocument/codeLens

lsp: 2025-06-20 22:21:39 UTC pid: 21976 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 14, method: textDocument/codeAction

lsp: 2025-06-20 22:21:39 UTC pid: 21976 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 16, method: textDocument/codeAction

lsp: 2025-06-20 22:21:39 UTC pid: 21976 - ThreadPoolExecutor-0_5 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000001B88CCCF560>

lsp: 2025-06-20 22:21:39 UTC pid: 21976 - ThreadPoolExecutor-0_1 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000001B88CCCF560>

lsp: 2025-06-20 22:21:39 UTC pid: 21976 - ThreadPoolExecutor-0_8 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000001B88CCCF560>

lsp: 2025-06-20 22:21:39 UTC pid: 21976 - ThreadPoolExecutor-0_7 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000001B88CCCF560>

lsp: 2025-06-20 22:21:39 UTC pid: 21976 - ThreadPoolExecutor-0_9 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000001B88CCCF560>

lsp: 2025-06-20 22:21:39 UTC pid: 21976 - ThreadPoolExecutor-0_10 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000001B88CCCF560>

lsp: 2025-06-20 22:22:00 UTC pid: 21976 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 21

lsp: 2025-06-20 22:22:00 UTC pid: 21976 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 29, method: textDocument/hover

lsp: 2025-06-20 22:22:00 UTC pid: 21976 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 30, method: textDocument/hover

lsp: 2025-06-20 22:22:00 UTC pid: 21976 - ThreadPoolExecutor-0_12 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000001B88CCCF560>

